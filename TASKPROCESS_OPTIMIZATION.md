# taskProcess 方法性能优化方案

## 问题分析

### 🔍 原始性能瓶颈

通过性能监控发现 `taskProcess` 方法耗时1.5秒，占总耗时的75%以上。

**主要问题**：

1. **重复数据库查询**：
   ```java
   // 第483行：又一次查询1000个任务
   Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
   
   // 第492行：在循环中为每个任务查询设备（1000次查询！）
   IPage<DataEquipmentOccupancyDTO> equipmentByTask = 
       dataEquipmentOccupancyService.getEquipmentByTask(-1, -1, task.getId());
   
   // 第499行：在循环中查询目标关系
   TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());
   ```

2. **算法复杂度问题**：
   - 外层循环：1000个任务
   - 内层循环：每个任务的设备数量
   - 复杂度：O(n*m)，其中n=1000，m=平均设备数

3. **无缓存机制**：每次调用都重新计算

## 优化方案

### ⚡ 核心优化策略

#### 1. 消除重复查询
```java
// 优化前：1000+次数据库查询
for (RequirementTask task : tasks) {
    IPage<DataEquipmentOccupancyDTO> equipmentByTask = 
        dataEquipmentOccupancyService.getEquipmentByTask(-1, -1, task.getId()); // 1000次查询
    TaskTargetRelation targetRelation = 
        taskTargetService.getById(task.getTargetRelationId()); // 1000次查询
}

// 优化后：使用已查询的数据 + 预构建映射
Map<String, List<DataEquipmentOccupancy>> taskEquipmentMap = buildTaskEquipmentMap(allEquipments);
Map<String, TaskTargetRelation> targetRelationMap = buildTargetRelationMap(tasks);
```

#### 2. 数据结构优化
```java
// 预构建高效查找映射
private Map<String, List<DataEquipmentOccupancy>> buildTaskEquipmentMap(List<DataEquipmentOccupancy> allEquipments) {
    return allEquipments.stream()
        .collect(Collectors.groupingBy(DataEquipmentOccupancy::getTaskId));
}

private Map<String, TaskTargetRelation> buildTargetRelationMap(List<RequirementTask> tasks) {
    List<String> relationIds = tasks.stream()
        .map(RequirementTask::getTargetRelationId)
        .distinct()
        .collect(Collectors.toList());
    
    Collection<TaskTargetRelation> relations = taskTargetService.listByIds(relationIds);
    return relations.stream()
        .collect(Collectors.toMap(TaskTargetRelation::getId, Function.identity()));
}
```

#### 3. 并行计算
```java
// 使用并行流处理1000个任务
List<RequirementTaskDTO> taskDTOS = tasks.parallelStream()
    .map(task -> processSingleTaskOptimized(...))
    .collect(Collectors.toList());
```

#### 4. 缓存机制
```java
// 缓存计算结果，避免重复计算
private final ConcurrentHashMap<String, List<RequirementTaskDTO>> taskProcessCache = new ConcurrentHashMap<>();

// 缓存key: taskIds + timePoint
String cacheKey = generateTaskProcessCacheKey(vo.getTaskIds(), vo.getTimePoint());
```

### 📊 性能提升预期

| 优化项 | 优化前 | 优化后 | 提升幅度 |
|--------|--------|--------|----------|
| 数据库查询次数 | 1000+ | 3-5次 | 99% |
| 算法复杂度 | O(n*m) | O(n) | 显著提升 |
| 并行处理 | 串行 | 并行 | 2-4倍 |
| 缓存命中 | 无 | 90%+ | 极大提升 |
| **总体耗时** | **1.5秒** | **< 200ms** | **87%** |

## 测试验证

### 🧪 性能测试接口

```bash
# 专门测试taskProcess性能
curl -X POST "http://localhost:8090/wrpt/performance/test/taskprocess" \
  -H "Content-Type: application/json" \
  -d '{
    "taskIds": ["task1", "task2", ...],
    "bzId": "test_bz_001",
    "timePoint": 0,
    "drag": true
  }'
```

### 📈 测试指标

1. **首次调用**：验证优化效果
2. **重复调用**：验证缓存效果
3. **缓存命中率**：应该达到90%+
4. **方法耗时分布**：taskProcess应该从1.5秒降到200ms以内

### 🔍 监控命令

```bash
# 获取详细性能报告
curl "http://localhost:8090/wrpt/performance/profile/report"

# 获取缓存统计
curl "http://localhost:8090/wrpt/performance/stats"

# 重置统计数据
curl -X POST "http://localhost:8090/wrpt/performance/profile/reset"
```

## 实现细节

### 🛠️ 关键优化点

1. **使用BaseDataContext**：
   - 复用已查询的任务数据
   - 复用已查询的设备数据
   - 避免重复数据库调用

2. **预构建映射表**：
   - 任务-设备映射：O(1)查找
   - 目标关系映射：O(1)查找
   - 设备详情映射：O(1)查找

3. **并行处理**：
   - 使用 `parallelStream()` 处理1000个任务
   - 线程安全的数据结构
   - 合理的线程池配置

4. **智能缓存**：
   - 基于taskIds+timePoint的缓存key
   - 2分钟过期时间
   - 异步清理机制

### ⚠️ 注意事项

1. **线程安全**：
   ```java
   synchronized (relation) { // 确保线程安全
       relation.addAll(relationTargets);
   }
   ```

2. **内存管理**：
   - 缓存有过期时间
   - 返回数据副本，避免修改缓存
   - 异步清理过期数据

3. **错误处理**：
   - 空值检查
   - 异常捕获
   - 降级处理

## 验证步骤

### 1. 基准测试
```bash
# 重置统计
curl -X POST "http://localhost:8090/wrpt/performance/profile/reset"

# 执行测试
curl -X POST "http://localhost:8090/wrpt/performance/test/taskprocess" \
  -H "Content-Type: application/json" \
  -d '{"taskIds": [...], "bzId": "test", "timePoint": 0, "drag": true}'
```

### 2. 查看结果
```bash
# 查看性能报告
curl "http://localhost:8090/wrpt/performance/profile/report"
```

### 3. 预期结果
- `taskProcessOptimized` 耗时 < 200ms
- 缓存命中率 > 90%
- 总接口耗时 < 1秒

## 后续优化

如果还有性能问题，可以考虑：

1. **数据库索引优化**
2. **Redis分布式缓存**
3. **异步处理**
4. **数据预计算**
5. **微服务拆分**
