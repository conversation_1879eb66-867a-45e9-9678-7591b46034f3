# handleDragOrPlay 方法性能优化方案

## 问题描述

当 `handleDragOrPlay` 方法传入大量 taskIds（如1000个）时，第一次存入缓存的时间会非常长，主要原因包括：

1. **数据库查询效率低**：大量 taskIds 导致数据库查询超时
2. **缓存策略不当**：一次性计算所有时间点（0到totalTime）的数据
3. **内存占用过大**：大量数据同时加载到内存中
4. **JSON序列化开销**：大量数据的序列化/反序列化操作耗时
5. **类型转换问题**：缓存数据反序列化时类型转换错误

## 优化方案：渐进式缓存 + 预测性预加载

### 使用场景分析
- **调用频率**：1秒钟调用一次（高频调用）
- **调用模式**：timePoint递增（0,1,2,3...），taskIds不变
- **性能要求**：首次可接受5-10秒，后续调用要很快
- **数据特征**：1000个taskIds，totalTime可能很长

### 核心改进

#### 1. 渐进式缓存策略
- **立即响应**：首次调用立即返回当前timePoint数据
- **后台计算**：启动渐进式预计算，分批计算所有时间点
- **智能批次**：每批计算50个时间点，避免系统阻塞
- **进度可控**：可以查询预计算进度

#### 2. 预测性预加载
- **预测窗口**：根据当前timePoint预测后续30秒的数据需求
- **提前准备**：异步预加载即将使用的数据
- **无缝体验**：用户感受不到延迟

#### 3. 分批数据库查询
- 将大量 taskIds 分批处理（默认100个一批）
- 避免单次查询数据量过大导致的超时问题

#### 4. 任务数据缓存
- 对相同 taskIds 组合的查询结果进行缓存（5分钟）
- 避免重复的数据库查询

#### 5. 类型转换优化
- 新增 `JsonConvertUtil` 工具类
- 解决缓存数据反序列化时的类型转换问题
- 安全处理 `List<TaskWithTargets>` 和 `List<DataEquipmentOccupancy>` 的转换

#### 6. 缓存预热服务（Plan B）
- 提供独立的预热接口 `/cache/warmup`
- 支持后台预先计算并缓存所有数据
- 提供预热进度查询和管理功能

### 主要新增组件

1. **ProgressiveCacheManager** - 渐进式缓存管理器
   - 管理渐进式预计算任务
   - 实现预测性预加载
   - 提供缓存状态监控

2. **TimePointDataComputer** - 时间点数据计算接口
   - 避免循环依赖
   - 统一数据计算逻辑

3. **CacheWarmupService** - 缓存预热服务
   - 提供后台预热功能
   - 支持预热进度管理
   - 缓存清理功能

4. **JsonConvertUtil** - 类型转换工具类
   - 解决缓存数据类型转换问题
   - 安全处理复杂对象转换

### 主要修改的方法

1. **getCacheOrCompute()** - 使用渐进式缓存管理器
2. **processDrag()** - 使用渐进式缓存管理器
3. **SituationServiceImpl** - 实现TimePointDataComputer接口

### 类型转换问题解决

**问题**：`List<TaskWithTargets> targets = (List<TaskWithTargets>) data.get("targets");` 
会报错 `jsonarray无法强制转换成TaskWithTargets对象`

**原因**：从缓存中获取的JSON数据反序列化后变成了基本的Map/List结构，而不是具体的Java对象类型。

**解决方案**：
```java
// 原来的写法（会报错）
List<TaskWithTargets> targets = (List<TaskWithTargets>) data.get("targets");

// 新的安全写法
List<TaskWithTargets> targets = JsonConvertUtil.getTaskWithTargetsList(data);
```

### 配置参数

在 `application.yml` 中可配置：

```yaml
performance:
  batch-size: 100                           # 数据库查询批次大小
  task-data-cache-minutes: 5                # 任务数据缓存时间（分钟）
  point-data-cache-multiplier: 2            # 点迹数据缓存时间倍数
  enable-async-precompute: true             # 是否启用异步预计算
  key-time-point-ratios: [0.0, 0.25, 0.5, 0.75, 1.0]  # 预计算时间点比例
  progressive-batch-size: 50                # 渐进式预计算批次大小
  predictive-window-size: 30                # 预测性预加载窗口大小
  progressive-delay-ms: 100                 # 渐进式预计算延迟时间
```

### 性能监控

新增 `PerformanceMonitor` 工具类，监控关键指标：
- 方法执行时间
- 调用次数
- 处理的 taskIds 数量
- 批处理模式使用次数

### 预期效果

1. **首次响应时间**：立即返回（< 1秒），满足1秒调用频率要求
2. **后续调用**：从缓存直接获取，响应时间 < 100ms
3. **渐进式优化**：后台自动计算，用户无感知
4. **预测性加速**：提前准备后续数据，无缝体验
5. **系统稳定性**：避免大查询导致的数据库压力
6. **内存可控**：分批计算，避免内存溢出
7. **类型安全**：解决缓存数据类型转换问题

### 使用示例

#### 方案A：自动渐进式缓存（推荐）
```java
// 第一次调用：立即返回timePoint=0的数据，启动后台渐进式计算
DragParamVO vo = new DragParamVO();
vo.setTaskIds(Arrays.asList("task1", "task2", ..., "task1000"));
vo.setTimePoint(0);
Map<String, Object> result1 = situationService.handleDragOrPlay(vo); // < 1秒

// 第二次调用：从缓存获取，同时预加载后续数据
vo.setTimePoint(1);
Map<String, Object> result2 = situationService.handleDragOrPlay(vo); // < 100ms

// 后续调用：都从缓存获取，响应极快
vo.setTimePoint(2);
Map<String, Object> result3 = situationService.handleDragOrPlay(vo); // < 50ms
```

#### 方案B：手动预热缓存
```java
// 1. 先调用预热接口
POST /wrpt/cache/warmup
Body: ["task1", "task2", ..., "task1000"]

// 2. 查询预热进度
GET /wrpt/cache/warmup/status/{warmupId}

// 3. 预热完成后，所有调用都很快
Map<String, Object> result = situationService.handleDragOrPlay(vo); // < 50ms
```

### 监控和调优

1. 通过 `PerformanceMonitor.printStats()` 查看性能统计
2. 根据实际使用情况调整配置参数
3. 监控缓存命中率和数据库查询频率

## 后续优化建议

1. **数据库索引优化**：为常用查询字段添加复合索引
2. **缓存预热**：系统启动时预加载热点数据
3. **数据压缩**：对缓存数据进行压缩存储
4. **分布式缓存**：使用 Redis Cluster 提升缓存性能
