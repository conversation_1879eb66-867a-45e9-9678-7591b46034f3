# handleDragOrPlay 方法性能优化方案

## 问题描述

当 `handleDragOrPlay` 方法传入大量 taskIds（如1000个）时，第一次存入缓存的时间会非常长，主要原因包括：

1. **数据库查询效率低**：大量 taskIds 导致数据库查询超时
2. **缓存策略不当**：一次性计算所有时间点（0到totalTime）的数据
3. **内存占用过大**：大量数据同时加载到内存中
4. **JSON序列化开销**：大量数据的序列化/反序列化操作耗时
5. **类型转换问题**：缓存数据反序列化时类型转换错误

## 优化方案1：分批处理和懒加载缓存

### 核心改进

#### 1. 分批数据库查询
- 将大量 taskIds 分批处理（默认100个一批）
- 避免单次查询数据量过大导致的超时问题
- 可通过配置调整批次大小

#### 2. 懒加载缓存策略
- **原来**：一次性计算所有时间点（0到totalTime）的数据并缓存
- **现在**：只计算当前需要的时间点数据
- 大幅减少首次加载时间和内存占用

#### 3. 异步预计算
- 在计算当前时间点数据后，异步预计算关键时间点（0%, 25%, 50%, 75%, 100%）
- 提升后续访问性能，不阻塞当前请求

#### 4. 任务数据缓存
- 对相同 taskIds 组合的查询结果进行缓存（5分钟）
- 避免重复的数据库查询

#### 5. 类型转换优化
- 新增 `JsonConvertUtil` 工具类
- 解决缓存数据反序列化时的类型转换问题
- 安全处理 `List<TaskWithTargets>` 和 `List<DataEquipmentOccupancy>` 的转换

### 主要修改的方法

1. **getDataByTasks()** - 添加分批处理逻辑
2. **getPointMap()** - 替换为 **getPointDataForTimePoint()** 懒加载方式
3. **getCacheOrCompute()** - 优化缓存策略
4. **processDrag()** - 使用新的懒加载缓存
5. **precomputeKeyTimePoints()** - 新增异步预计算方法
6. **JsonConvertUtil** - 新增类型转换工具类，解决缓存数据类型转换问题

### 类型转换问题解决

**问题**：`List<TaskWithTargets> targets = (List<TaskWithTargets>) data.get("targets");` 
会报错 `jsonarray无法强制转换成TaskWithTargets对象`

**原因**：从缓存中获取的JSON数据反序列化后变成了基本的Map/List结构，而不是具体的Java对象类型。

**解决方案**：
```java
// 原来的写法（会报错）
List<TaskWithTargets> targets = (List<TaskWithTargets>) data.get("targets");

// 新的安全写法
List<TaskWithTargets> targets = JsonConvertUtil.getTaskWithTargetsList(data);
```

### 配置参数

在 `application-performance.yml` 中可配置：

```yaml
performance:
  batch-size: 100                           # 数据库查询批次大小
  task-data-cache-minutes: 5                # 任务数据缓存时间（分钟）
  point-data-cache-multiplier: 2            # 点迹数据缓存时间倍数
  enable-async-precompute: true             # 是否启用异步预计算
  key-time-point-ratios: [0.0, 0.25, 0.5, 0.75, 1.0]  # 预计算时间点比例
```

### 性能监控

新增 `PerformanceMonitor` 工具类，监控关键指标：
- 方法执行时间
- 调用次数
- 处理的 taskIds 数量
- 批处理模式使用次数

### 预期效果

1. **首次加载时间**：从原来的几十秒减少到几秒
2. **内存占用**：大幅减少，只加载必要数据
3. **后续访问**：通过异步预计算，常用时间点访问更快
4. **系统稳定性**：避免大查询导致的数据库压力
5. **类型安全**：解决缓存数据类型转换问题

### 使用示例

```java
// 原来：传入1000个taskIds，需要等待很长时间
DragParamVO vo = new DragParamVO();
vo.setTaskIds(Arrays.asList("task1", "task2", ..., "task1000"));
vo.setTimePoint(100);

// 现在：只计算timePoint=100的数据，快速返回
// 同时异步预计算其他关键时间点
Map<String, Object> result = situationService.handleDragOrPlay(vo);
```

### 监控和调优

1. 通过 `PerformanceMonitor.printStats()` 查看性能统计
2. 根据实际使用情况调整配置参数
3. 监控缓存命中率和数据库查询频率

## 后续优化建议

1. **数据库索引优化**：为常用查询字段添加复合索引
2. **缓存预热**：系统启动时预加载热点数据
3. **数据压缩**：对缓存数据进行压缩存储
4. **分布式缓存**：使用 Redis Cluster 提升缓存性能
