# handleDragOrPlay 性能排查指南

## 当前问题
接口响应时间：1.5-2秒，目标：<1秒

## 排查步骤

### 1. 快速性能测试

```bash
# 单次调用测试
curl -X POST "http://localhost:8090/wrpt/performance/test/single" \
  -H "Content-Type: application/json" \
  -d '{
    "taskIds": ["task1", "task2", ...],
    "bzId": "test_bz_001",
    "timePoint": 0,
    "drag": true
  }'

# 序列调用测试（模拟实际使用场景）
curl -X POST "http://localhost:8090/wrpt/performance/test/sequence" \
  -H "Content-Type: application/json" \
  -d '{
    "taskIds": ["task1", "task2", ...],
    "bzId": "test_bz_001",
    "startTime": 0,
    "endTime": 10
  }'
```

### 2. 获取详细性能分析

```bash
# 获取性能统计
curl "http://localhost:8090/wrpt/performance/stats"

# 获取详细性能分析报告
curl "http://localhost:8090/wrpt/performance/profile/report"

# 获取内存使用情况
curl "http://localhost:8090/wrpt/performance/test/memory"
```

### 3. 重置统计数据

```bash
# 重置性能统计
curl -X POST "http://localhost:8090/wrpt/performance/profile/reset"
```

## 性能优化点

### 已优化的部分

1. **基础数据查询优化**
   - 一次性查询所有基础数据，避免重复查询
   - 使用 `BaseDataContext` 缓存查询结果
   - 消除 `getTotalTime()` 和 `satelliteCalculate()` 的重复查询

2. **并行计算**
   - 卫星计算使用异步执行
   - 减少串行等待时间

3. **航迹数据缓存**
   - 缓存已计算的航迹数据
   - 避免重复的 `obtainTrack()` 计算

### 可能的性能瓶颈

1. **数据库查询**
   - `requirementTaskService.listByIds()` - 1000个任务查询
   - `dataGeneralService.listByIds()` - 设备数据查询
   - 建议：添加数据库查询日志，检查SQL执行时间

2. **航迹计算**
   - `obtainTrack()` 方法可能很耗时
   - 建议：分析航迹插值算法的复杂度

3. **后置处理**
   - `postFilterData()` - 数据过滤
   - `calculateEquipmentScout()` - 设备侦查范围计算
   - `taskProcess()` - 任务进度计算

## 排查命令

### 1. 检查数据库性能

```sql
-- 检查任务查询性能
EXPLAIN SELECT * FROM requirement_task WHERE id IN ('task1', 'task2', ...);

-- 检查是否有合适的索引
SHOW INDEX FROM requirement_task;
SHOW INDEX FROM data_equipment_occupancy;
SHOW INDEX FROM data_general;
```

### 2. 检查JVM性能

```bash
# 检查GC情况
jstat -gc [pid] 1s 10

# 检查内存使用
jmap -histo [pid] | head -20

# 检查线程状态
jstack [pid] | grep -A 5 -B 5 "handleDragOrPlay"
```

### 3. 应用层监控

```java
// 在关键方法中添加性能监控
performanceProfiler.startProfiling("methodName");
// ... 方法执行
performanceProfiler.endProfiling("methodName");
```

## 预期性能提升

### 优化前后对比

| 操作 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 基础数据查询 | 多次查询 | 一次查询 | 50-70% |
| 卫星计算 | 串行执行 | 并行执行 | 30-50% |
| 航迹计算 | 每次重算 | 缓存复用 | 80-90% |

### 目标性能

- **首次调用**：< 1秒
- **后续调用**：< 500ms
- **缓存命中**：< 200ms

## 进一步优化建议

1. **数据库优化**
   - 添加复合索引
   - 使用批量查询
   - 考虑读写分离

2. **缓存策略**
   - Redis集群
   - 本地缓存 + 分布式缓存
   - 缓存预热

3. **算法优化**
   - 航迹插值算法优化
   - 并行计算框架
   - 近似计算

4. **架构优化**
   - 微服务拆分
   - 异步处理
   - 消息队列

## 监控告警

建议设置以下监控指标：

- 接口响应时间 > 1秒 告警
- 数据库查询时间 > 500ms 告警
- 内存使用率 > 80% 告警
- GC频率过高告警
