# 任务目标和设备信息合并接口文档

## 概述

本文档描述了新增的批量查询接口，该接口合并了原有的两个单任务查询接口：
- `TaskTargetController.listByTask` - 根据单个任务ID查询目标及航迹信息
- `DataEquipmentOccupancyController.getEquipmentByTask` - 根据单个任务ID分页查询设备占用信息

## 新接口

### 接口地址
```
POST /task/target/listByTaskIds
```

### 请求参数

#### 请求体 (JSON)
```json
["taskId1", "taskId2", "taskId3"]
```

#### 查询参数
- `isInterpolated` (可选): Boolean类型，是否插值，默认为true

### 请求示例
```bash
curl -X POST "http://localhost:8080/task/target/listByTaskIds?isInterpolated=true" \
  -H "Content-Type: application/json" \
  -d '["task001", "task002", "task003"]'
```

### 响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "taskId": "task001",
      "target": {
        "id": "target001",
        "taskId": "task001",
        "generalId": "general001",
        "targetId": "target001",
        "name": "目标名称",
        "longitude": 116.123456,
        "latitude": 39.123456,
        "altitude": 1000.0,
        "targetTrack": [
          {
            "id": "track001",
            "longitude": 116.123456,
            "latitude": 39.123456,
            "altitude": 1000.0,
            "time": "2023-12-01T10:00:00"
          }
        ],
        "taskCoverPoint": [[116.1, 39.1], [116.2, 39.2]]
      },
      "equipments": [
        {
          "id": "equipment001",
          "taskId": "task001",
          "generalId": "general001",
          "equipmentId": "eq001",
          "areaName": "资源域名称",
          "dataType": 1,
          "type": "固定站",
          "equipmentDetail": {
            "name": "设备名称",
            "location": "设备位置"
          },
          "startTime": "2023-12-01T10:00:00",
          "endTime": "2023-12-01T12:00:00"
        }
      ]
    }
  ]
}
```

## 数据结构说明

### TaskCombinedDataDTO
- `taskId`: 任务ID
- `target`: 任务目标信息 (TaskTargetRelationDTO)
- `equipments`: 设备占用信息列表 (List<DataEquipmentOccupancyDTO>)

### 与原接口的对比

| 原接口 | 新接口 | 变化 |
|--------|--------|------|
| 单任务查询 | 批量任务查询 | 支持多任务ID |
| 分页查询设备 | 非分页查询设备 | 移除分页，返回所有相关设备 |
| 两个独立接口 | 一个合并接口 | 减少网络请求次数 |

## 性能优化

1. **批量查询**: 使用IN查询减少数据库访问次数
2. **数据预加载**: 一次性加载相关的目标、航迹、设备信息
3. **内存优化**: 使用Map进行数据分组，提高查询效率

## 使用建议

1. 建议每次查询的任务ID数量不超过100个，以避免性能问题
2. 如果需要分页功能，建议在客户端进行分页处理
3. 对于大量数据的场景，可以考虑异步处理

## 兼容性说明

- 原有的单任务查询接口保持不变，确保向后兼容
- 新接口可以与现有接口并行使用
- 建议新开发的功能优先使用批量接口以提高性能
