package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.vo.DragParamVO;
import com.gy.show.service.SituationService;
import com.gy.show.util.PerformanceMonitor;
import com.gy.show.util.PerformanceProfiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能测试控制器
 */
@Api(tags = "性能测试")
@RestController
@RequestMapping("/performance")
@Slf4j
public class PerformanceTestController {
    
    @Autowired
    private SituationService situationService;
    
    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private PerformanceProfiler performanceProfiler;
    
    @PostMapping("/test/single")
    @ApiOperation("单次性能测试")
    public Result testSingleCall(@RequestBody DragParamVO vo) {
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, Object> result = situationService.handleDragOrPlay(vo);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            Map<String, Object> response = new HashMap<>();
            response.put("duration", duration + "ms");
            response.put("taskCount", vo.getTaskIds().size());
            response.put("timePoint", vo.getTimePoint());
            response.put("success", true);
            
            log.info("性能测试完成 - 耗时: {}ms, 任务数: {}, 时间点: {}", 
                duration, vo.getTaskIds().size(), vo.getTimePoint());
            
            return Result.ok(response);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("性能测试失败 - 耗时: {}ms", duration, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("duration", duration + "ms");
            response.put("error", e.getMessage());
            response.put("success", false);
            
            return Result.error("测试失败", response);
        }
    }
    
    @PostMapping("/test/sequence")
    @ApiOperation("序列性能测试 - 模拟连续调用")
    public Result testSequenceCalls(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            java.util.List<String> taskIds = (java.util.List<String>) params.get("taskIds");
            String bzId = (String) params.get("bzId");
            Integer startTime = (Integer) params.getOrDefault("startTime", 0);
            Integer endTime = (Integer) params.getOrDefault("endTime", 10);
            
            java.util.List<Map<String, Object>> results = new java.util.ArrayList<>();
            
            for (int timePoint = startTime; timePoint <= endTime; timePoint++) {
                DragParamVO vo = new DragParamVO();
                vo.setTaskIds(taskIds);
                vo.setBzId(bzId);
                vo.setTimePoint(timePoint);
                
                long callStart = System.currentTimeMillis();
                situationService.handleDragOrPlay(vo);
                long callEnd = System.currentTimeMillis();
                
                Map<String, Object> callResult = new HashMap<>();
                callResult.put("timePoint", timePoint);
                callResult.put("duration", (callEnd - callStart) + "ms");
                results.add(callResult);
                
                log.info("序列测试 - 时间点: {}, 耗时: {}ms", timePoint, callEnd - callStart);
            }
            
            return Result.ok(results);
            
        } catch (Exception e) {
            log.error("序列性能测试失败", e);
            return Result.error("序列测试失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/stats")
    @ApiOperation("获取性能统计")
    public Result getPerformanceStats() {
        try {
            performanceMonitor.printStats();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("handleDragOrPlay_calls", performanceMonitor.getCounter("handleDragOrPlay_calls"));
            stats.put("cache_hit", performanceMonitor.getCounter("cache_hit"));
            stats.put("cache_miss", performanceMonitor.getCounter("cache_miss"));
            stats.put("simple_cache_hit", performanceMonitor.getCounter("simple_cache_hit"));
            stats.put("simple_cache_miss", performanceMonitor.getCounter("simple_cache_miss"));
            stats.put("handleDragOrPlay_duration", performanceMonitor.getTimer("handleDragOrPlay"));
            stats.put("getCacheOrCompute_duration", performanceMonitor.getTimer("getCacheOrCompute"));
            stats.put("getPointDataForTimePoint_duration", performanceMonitor.getTimer("getPointDataForTimePoint"));
            
            return Result.ok(stats);
            
        } catch (Exception e) {
            log.error("获取性能统计失败", e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/stats/reset")
    @ApiOperation("重置性能统计")
    public Result resetPerformanceStats() {
        try {
            performanceMonitor.reset();
            return Result.ok("性能统计已重置");
        } catch (Exception e) {
            log.error("重置性能统计失败", e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/test/memory")
    @ApiOperation("内存使用情况")
    public Result getMemoryUsage() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("totalMemory", formatBytes(totalMemory));
            memoryInfo.put("usedMemory", formatBytes(usedMemory));
            memoryInfo.put("freeMemory", formatBytes(freeMemory));
            memoryInfo.put("maxMemory", formatBytes(maxMemory));
            memoryInfo.put("usagePercentage", String.format("%.2f%%", (double) usedMemory / maxMemory * 100));
            
            return Result.ok(memoryInfo);
            
        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            return Result.error("获取内存信息失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/profile/report")
    @ApiOperation("获取详细性能分析报告")
    public Result getProfileReport() {
        try {
            String report = performanceProfiler.getPerformanceReport();
            List<String> slowestMethods = performanceProfiler.getSlowestMethods(10);

            Map<String, Object> result = new HashMap<>();
            result.put("report", report);
            result.put("slowestMethods", slowestMethods);

            return Result.ok(result);

        } catch (Exception e) {
            log.error("获取性能分析报告失败", e);
            return Result.error("获取报告失败: " + e.getMessage());
        }
    }

    @PostMapping("/profile/reset")
    @ApiOperation("重置性能分析数据")
    public Result resetProfile() {
        try {
            performanceProfiler.reset();
            performanceMonitor.reset();
            return Result.ok("性能分析数据已重置");
        } catch (Exception e) {
            log.error("重置性能分析数据失败", e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }

    @PostMapping("/test/taskprocess")
    @ApiOperation("专门测试taskProcess方法性能")
    public Result testTaskProcessPerformance(@RequestBody DragParamVO vo) {
        try {
            Map<String, Object> results = new HashMap<>();

            // 测试多次调用，观察缓存效果
            for (int i = 0; i < 3; i++) {
                long startTime = System.currentTimeMillis();

                Map<String, Object> result = situationService.handleDragOrPlay(vo);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                results.put("call_" + (i + 1) + "_duration", duration + "ms");

                // 获取详细的方法耗时
                Map<String, Object> methodTimes = new HashMap<>();
                methodTimes.put("handleDragOrPlay", performanceMonitor.getTimer("handleDragOrPlay") / 1_000_000 + "ms");
                methodTimes.put("task_process", performanceMonitor.getTimer("task_process") / 1_000_000 + "ms");
                methodTimes.put("base_data_query", performanceMonitor.getTimer("base_data_query") / 1_000_000 + "ms");
                methodTimes.put("satellite_calculate", performanceMonitor.getTimer("satellite_calculate") / 1_000_000 + "ms");

                results.put("call_" + (i + 1) + "_method_times", methodTimes);

                // 短暂延迟
                Thread.sleep(100);
            }

            // 获取缓存命中率
            long cacheHit = performanceMonitor.getCounter("taskProcess_cache_hit");
            long cacheMiss = performanceMonitor.getCounter("taskProcess_cache_miss");
            double hitRate = cacheMiss > 0 ? (double) cacheHit / (cacheHit + cacheMiss) * 100 : 0;

            results.put("cache_hit_rate", String.format("%.2f%%", hitRate));
            results.put("cache_hit_count", cacheHit);
            results.put("cache_miss_count", cacheMiss);

            return Result.ok(results);

        } catch (Exception e) {
            log.error("taskProcess性能测试失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
        return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
    }
}
