package com.gy.show.controller;

import cn.hutool.core.collection.CollUtil;
import com.gy.show.common.BaseController;
import com.gy.show.common.Result;
import com.gy.show.entity.dto.DataEquipmentOccupancyDTO;
import com.gy.show.entity.dto.TaskCombinedDataDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.service.DataEquipmentOccupancyService;
import com.gy.show.service.TaskTargetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务目标相关接口
 */
@RestController
@RequestMapping("/task/target")
@Api(tags = "任务目标相关接口")
public class TaskTargetController extends BaseController {

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @ApiOperation("根据任务ID查询所有目标及航迹")
    @GetMapping("/listByTask")
    public Result listByTask(@RequestParam("taskId") String taskId,
                             @RequestParam(value = "isInterpolated", required = false, defaultValue = "true") Boolean isInterpolated) {
        TaskTargetRelationDTO result = taskTargetService.listByTask(taskId, isInterpolated);
        return Result.ok(result);
    }

    @ApiOperation("批量根据任务ID查询目标及设备信息")
    @PostMapping("/listByTaskIds")
    public Result listByTaskIds(@RequestBody List<String> taskIds,
                                @RequestParam(value = "isInterpolated", required = false, defaultValue = "true") Boolean isInterpolated) {
        if (CollUtil.isEmpty(taskIds)) {
            return Result.ok(Collections.emptyList());
        }

        // 批量查询任务目标信息
        Map<String, TaskTargetRelationDTO> targetMap = taskTargetService.listTargetsByTaskIds(taskIds, isInterpolated);

        // 批量查询设备占用信息
        Map<String, List<DataEquipmentOccupancyDTO>> equipmentMap = dataEquipmentOccupancyService.getEquipmentByTaskIds(taskIds);

        // 合并结果
        List<TaskCombinedDataDTO> result = taskIds.stream()
                .map(taskId -> {
                    TaskCombinedDataDTO combinedData = new TaskCombinedDataDTO();
                    combinedData.setTaskId(taskId);
                    combinedData.setTarget(targetMap.get(taskId));
                    combinedData.setEquipments(equipmentMap.getOrDefault(taskId, Collections.emptyList()));
                    return combinedData;
                })
                .collect(Collectors.toList());

        return Result.ok(result);
    }

    @ApiOperation("新增目标接口，包含目标航迹以及目标位置等信息")
    @PostMapping("/storeTarget")
    public Result storeTarget(@Valid @RequestBody TaskTargetRelationDTO taskTargetRelationDTO) {
        taskTargetService.storeTarget(taskTargetRelationDTO);
        return Result.ok();
    }

    @ApiOperation("修改目标接口，包含及目标位置、航迹宽度颜色等信息")
    @PutMapping("/updateTarget")
    public Result updateTarget(@Valid @RequestBody TaskTargetRelationDTO taskTargetRelationDTO) {
        taskTargetService.updateTarget(taskTargetRelationDTO);
        return Result.ok();
    }

    @ApiOperation("删除目标航迹接口")
    @DeleteMapping("/removeTrack")
    public Result removeTrack(@RequestParam("relationId") String relationId) {
        taskTargetService.removeTrack(relationId);
        return Result.ok();
    }

    @ApiOperation("根据目标ID和任务ID删除目标,同步会删除关联航迹")
    @DeleteMapping("/removeTargetAndTrack")
    public Result removeTargetAndTrack(@RequestParam("taskId") String taskId,
                                       @RequestParam("targetId") String targetId) {
        taskTargetService.removeTargetAndTrack(taskId, targetId);
        return Result.ok();
    }
}
