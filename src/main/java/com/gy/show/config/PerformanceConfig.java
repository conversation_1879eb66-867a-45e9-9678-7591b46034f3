package com.gy.show.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 性能优化相关配置
 */
@Configuration
@ConfigurationProperties(prefix = "performance")
public class PerformanceConfig {

    /**
     * 数据库查询批次大小
     */
    private int batchSize = 100;

    /**
     * 任务数据缓存时间（分钟）
     */
    private int taskDataCacheMinutes = 5;

    /**
     * 点迹数据缓存时间倍数
     */
    private int pointDataCacheMultiplier = 2;

    /**
     * 是否启用异步预计算
     */
    private boolean enableAsyncPrecompute = true;

    /**
     * 预计算关键时间点的比例
     */
    private double[] keyTimePointRatios = {0.0, 0.25, 0.5, 0.75, 1.0};

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public int getTaskDataCacheMinutes() {
        return taskDataCacheMinutes;
    }

    public void setTaskDataCacheMinutes(int taskDataCacheMinutes) {
        this.taskDataCacheMinutes = taskDataCacheMinutes;
    }

    public int getPointDataCacheMultiplier() {
        return pointDataCacheMultiplier;
    }

    public void setPointDataCacheMultiplier(int pointDataCacheMultiplier) {
        this.pointDataCacheMultiplier = pointDataCacheMultiplier;
    }

    public boolean isEnableAsyncPrecompute() {
        return enableAsyncPrecompute;
    }

    public void setEnableAsyncPrecompute(boolean enableAsyncPrecompute) {
        this.enableAsyncPrecompute = enableAsyncPrecompute;
    }

    public double[] getKeyTimePointRatios() {
        return keyTimePointRatios;
    }

    public void setKeyTimePointRatios(double[] keyTimePointRatios) {
        this.keyTimePointRatios = keyTimePointRatios;
    }
}
