package com.gy.show.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 任务合并数据DTO
 * 包含任务的目标信息和设备占用信息
 */
@Data
public class TaskCombinedDataDTO implements Serializable {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务目标关联信息
     */
    private TaskTargetRelationDTO target;
    
    /**
     * 设备占用信息列表
     */
    private List<DataEquipmentOccupancyDTO> equipments;
    
    private static final long serialVersionUID = 1L;
}
