package com.gy.show.entity.context;

import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.TaskWithTargets;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 基础数据上下文
 * 用于缓存一次查询的基础数据，避免重复查询数据库
 */
@Data
public class BaseDataContext {
    
    /**
     * 任务列表
     */
    private List<RequirementTask> tasks;
    
    /**
     * 任务目标数据
     */
    private List<TaskWithTargets> taskWithTargets;
    
    /**
     * 设备占用数据
     */
    private List<DataEquipmentOccupancy> equipments;
    
    /**
     * 通用数据（包含卫星等）
     */
    private List<DataGeneral> dataGenerals;
    
    /**
     * 卫星数据
     */
    private List<DataGeneral> satellites;
    
    /**
     * 总时长（秒）
     */
    private Integer totalTime;
    
    /**
     * 最早开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 最晚结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 任务ID列表
     */
    private List<String> taskIds;
    
    /**
     * 原始数据Map（用于兼容现有代码）
     */
    private Map<String, Object> rawData;
    
    /**
     * 构造函数
     */
    public BaseDataContext(List<String> taskIds) {
        this.taskIds = taskIds;
    }
}
