package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.common.ServiceException;
import com.gy.show.config.PerformanceConfig;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.*;
import com.gy.show.entity.context.BaseDataContext;
import com.gy.show.entity.vo.DragParamVO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.entity.vo.DragReturnVO;
import com.gy.show.entity.vo.PointVO;
import com.gy.show.entity.vo.TrackVO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.runnner.PerformanceMonitor;
import com.gy.show.service.*;
import com.gy.show.util.*;
import com.gy.show.ws.GlobalServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.geom.Point2D;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.gy.show.constants.CacheConstant.POINT_CACHE_PREFIX;

@Slf4j
@Service
public class SituationServiceImpl implements SituationService, TimePointDataComputer {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private RequirementTargetTrackService trackService;

    @Autowired
    private TaskEquipmentRelationService taskEquipmentRelationService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private SatelliteService satelliteService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private GlobalServer globalServer;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private PerformanceConfig performanceConfig;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private ProgressiveCacheManager progressiveCacheManager;

    @Autowired
    private SimpleCacheStrategy simpleCacheStrategy;

    @Autowired
    private PerformanceProfiler performanceProfiler;

    private static final Integer SHARD_VALUE = 1;

    @Override
    public Map<String, Object> getDataByTasks(List<String> taskIds) {
        long startTime = performanceMonitor.startTimer("getDataByTasks");
        performanceMonitor.addToCounter("getDataByTasks_taskIds", taskIds.size());

        // 分批处理大量taskIds，避免数据库查询超时
        final int BATCH_SIZE = performanceConfig.getBatchSize();
        if (taskIds.size() > BATCH_SIZE) {
            performanceMonitor.incrementCounter("getDataByTasks_batch_mode");
            Map<String, Object> result = getDataByTasksBatch(taskIds, BATCH_SIZE);
            performanceMonitor.endTimer("getDataByTasks");
            return result;
        }

        Collection<RequirementTask> tasks = requirementTaskService.listByIds(taskIds);
        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        Collection<TaskTargetRelation> taskTargetRelations = taskTargetService.listByIds(relationIds);
        Map<String, List<TaskTargetRelation>> groupByRelation0 = taskTargetRelations.stream()
                .collect(Collectors.groupingBy(TaskTargetRelation::getId));

        // 查询目标的航迹信息
        List<RequirementTargetTrack> tracks = trackService.queryTrackByIds(relationIds);

        Map<String, List<RequirementTargetTrack>> groupByRelation1 = tracks.stream()
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        // 构建任务和其目标、航迹的层级结构
        List<TaskWithTargets> taskWithTargetsList = tasks.stream()
                .map(task -> {
                    List<TargetWithTracks> targetsWithTracks = groupByRelation0.getOrDefault(task.getTargetRelationId(), Collections.emptyList()).stream()
                            .map(target -> new TargetWithTracks(target, groupByRelation1.getOrDefault(target.getId(), Collections.emptyList()), target.getType()))
                            .collect(Collectors.toList());

                    TaskWithTargets taskWithTargets = new TaskWithTargets(task, targetsWithTracks);
                    taskWithTargets.setTargetId(targetsWithTracks.get(0).getTarget().getTargetId());
                    return taskWithTargets;
                })
                .collect(Collectors.toList());

        log.info("目标数据查询结束,查询结果:{}", taskWithTargetsList);

        /**
         * 查询任务设备关联表
         */
        List<DataEquipmentOccupancy> taskEquipmentRelations = dataEquipmentOccupancyService.queryEquipmentByTaskIds(taskIds);

        Map<String, Object> result = new HashMap<>();
        result.put("targets", taskWithTargetsList);
        result.put("equipments", taskEquipmentRelations);

        performanceMonitor.endTimer("getDataByTasks");
        return result;
    }

    /**
     * 分批处理大量taskIds的数据查询
     */
    private Map<String, Object> getDataByTasksBatch(List<String> taskIds, int batchSize) {
        List<TaskWithTargets> allTaskWithTargets = new ArrayList<>();
        List<DataEquipmentOccupancy> allEquipmentRelations = new ArrayList<>();

        // 分批处理
        for (int i = 0; i < taskIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, taskIds.size());
            List<String> batchTaskIds = taskIds.subList(i, endIndex);

            Map<String, Object> batchResult = getDataByTasks(batchTaskIds);

            // 使用安全的类型转换
            List<TaskWithTargets> batchTargets = JsonConvertUtil.getTaskWithTargetsList(batchResult);
            List<DataEquipmentOccupancy> batchEquipments = JsonConvertUtil.getDataEquipmentOccupancyList(batchResult);

            if (batchTargets != null) {
                allTaskWithTargets.addAll(batchTargets);
            }
            if (batchEquipments != null) {
                allEquipmentRelations.addAll(batchEquipments);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("targets", allTaskWithTargets);
        result.put("equipments", allEquipmentRelations);
        return result;
    }

    /**
     * 带缓存的任务数据查询，避免重复查询相同的任务组合
     */
    private Map<String, Object> getDataByTasksWithCache(List<String> taskIds) {
        // 生成缓存key，基于taskIds的hash
        String taskIdsKey = String.join(",", taskIds.stream().sorted().collect(Collectors.toList()));
        String cacheKey = "task_data:" + taskIdsKey.hashCode();

        // 尝试从缓存获取
        String cachedData = RedisUtil.StringOps.get(cacheKey);
        if (StringUtils.isNotBlank(cachedData)) {
            try {
                Map<String, Object> cachedResult = JSON.parseObject(cachedData, Map.class);
                // 确保缓存的数据能正确转换为所需的对象类型
                return cachedResult;
            } catch (Exception e) {
                log.warn("解析缓存的任务数据失败，将重新查询", e);
            }
        }

        // 缓存未命中，查询数据库
        Map<String, Object> result = getDataByTasks(taskIds);

        // 缓存结果
        try {
            RedisUtil.StringOps.setEx(cacheKey, JSON.toJSONString(result),
                    performanceConfig.getTaskDataCacheMinutes(), TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("缓存任务数据失败", e);
        }

        return result;
    }

    @Override
    public Map<String, Object> getData(String id) {
        /**
         * 查询目标以及航迹
         * 1 先查询需求下有哪些任务
         * 2 根据任务查询相关联的目标列表
         * 3 查询目标的航迹信息
         * */
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(id);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());

        Map<String, Object> data = new HashMap<>();
        List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.list(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                .in(DataEquipmentOccupancy::getTaskId, taskIds));

        List<DataEquipmentOccupancyDTO> equipments = dataEquipmentOccupancies.stream()
                .map(DataEquipmentOccupancy::convert)
                .collect(Collectors.toList());

        // 主表ID集合
        List<String> generalIds = dataEquipmentOccupancies
                .stream().map(DataEquipmentOccupancy::getGeneralId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(generalIds)) {
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);
            Map<String, List<DataGeneral>> groupById = dataGenerals.stream()
                    .collect(Collectors.groupingBy(DataGeneral::getId));

            for (DataEquipmentOccupancyDTO record : equipments) {
                List<DataGeneral> generalList = groupById.get(record.getGeneralId());

                if (CollUtil.isNotEmpty(generalList)) {
                    DataGeneral general = generalList.get(0);

                    Map<String, Object> equipmentDetail = commonMapper.getOne(general.getTableName(), record.getEquipmentId());
                    record.setEquipmentDetail(equipmentDetail);
                    record.setDataType(general.getDataType());
                    record.setType(general.getTableComment());
                }
            }
        }

        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        Collection<TaskTargetRelation> taskTargetRelations = taskTargetService.listByIds(relationIds);

        List<TaskTargetRelationDTO> targets = taskTargetRelations.stream()
                .map(task -> {
                    TaskTargetRelationDTO targetRelationDTO = task.convert();
                    DataGeneral general = dataGeneralService.getById(task.getGeneralId());
                    targetRelationDTO.setDataType(general.getDataType());
                    targetRelationDTO.setTargetType(general.getTableComment());
                    return targetRelationDTO;
                }).collect(Collectors.toList());


        // 设备去重
        List<DataEquipmentOccupancyDTO> distinctEquipments = new ArrayList<>(equipments.stream()
                .collect(Collectors.toMap(
                        DataEquipmentOccupancyDTO::getEquipmentId,
                        Function.identity(),
                        (e, r) -> e
                )).values());

        data.put("targets", targets);
        data.put("equipments", distinctEquipments);

        return data;

//        List<TaskTargetRelation> relationList = taskTargetService.listByTaskIds(taskIds);
//        Map<String, List<TaskTargetRelation>> groupByRelation0 = relationList.stream()
//                .collect(Collectors.groupingBy(TaskTargetRelation::getTaskId));
//
//        // 查询目标的航迹信息
//        List<String> relationIds = relationList.stream()
//                .map(TaskTargetRelation::getId)
//                .collect(Collectors.toList());
//
//        List<RequirementTargetTrack> tracks = trackService.queryTrackByIds(relationIds);
//
//        Map<String, List<RequirementTargetTrack>> groupByRelation1 = tracks.stream()
//                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));
//
//        // 构建任务和其目标、航迹的层级结构
//        List<TaskWithTargets> taskWithTargetsList = tasks.stream()
//                .map(task -> {
//                    List<TargetWithTracks> targetsWithTracks = groupByRelation0.getOrDefault(task.getId(), Collections.emptyList()).stream()
//                            .map(target -> new TargetWithTracks(target, groupByRelation1.getOrDefault(target.getId(), Collections.emptyList()), target.getType()))
//                            .collect(Collectors.toList());
//                    return new TaskWithTargets(task, targetsWithTracks);
//                })
//                .collect(Collectors.toList());
//
//        log.info("目标数据查询结束,查询结果:{}", taskWithTargetsList);
//
//        /**
//         * 查询任务设备关联表
//         */
//        List<DataEquipmentOccupancy> taskEquipmentRelations = dataEquipmentOccupancyService.queryEquipmentByTaskIds(taskIds);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("targets", taskWithTargetsList);
//        result.put("equipments", taskEquipmentRelations);
//
//        // 返回构建的层级结构
//        return result;
    }

    @Override
    public Map<String, Object> handleDragOrPlay(DragParamVO vo) {
        performanceProfiler.startProfiling("handleDragOrPlay");
        long startTime = performanceMonitor.startTimer("handleDragOrPlay");
        performanceMonitor.incrementCounter("handleDragOrPlay_calls");
        performanceMonitor.addToCounter("taskIds_count", vo.getTaskIds().size());

        try {
            // 创建返回结果的map
            Map<String, Object> resultMap = new HashMap<>();

            // 将时间点从毫秒转换为秒，并设置默认步长为1
            vo.setTimePoint(vo.getTimePoint());
            vo.setStep(Objects.nonNull(vo.getStep()) ? vo.getStep() : 1);

            // 优化：一次性获取所有需要的基础数据，避免重复查询
            long dataQueryStart = performanceMonitor.startTimer("base_data_query");
            BaseDataContext dataContext = getBaseDataContext(vo);
            performanceMonitor.endTimer("base_data_query");

            // 根据是否是拖拽操作调用不同的处理方法
            long dragProcessStart = performanceMonitor.startTimer("drag_process");
            List<DragReturnVO> resultList = vo.getDrag() ?
                processDrag(vo, dataContext.getTotalTime()) :
                processPlay(vo, dataContext.getTotalTime());
            performanceMonitor.endTimer("drag_process");

            // 并行计算其他数据，提升性能
            CompletableFuture<Map<String, Map<String, Object>>> satelliteFuture =
                CompletableFuture.supplyAsync(() -> {
                    long satelliteStart = performanceMonitor.startTimer("satellite_calculate");
                    try {
                        noticeClientMessage("开始计算资源数据...", 60);
                        Map<String, Map<String, Object>> result = satelliteCalculateOptimized(dataContext);
                        noticeClientMessage("资源数据计算完成...", 100);
                        return result;
                    } finally {
                        performanceMonitor.endTimer("satellite_calculate");
                    }
                });

            // 后置过滤器
            long filterStart = performanceMonitor.startTimer("post_filter");
            List<DragReturnVO> filterResult = postFilterData(resultList, vo);
            performanceMonitor.endTimer("post_filter");

            // 等待卫星计算完成
            Map<String, Map<String, Object>> satellite = satelliteFuture.get();

            // 计算目标是否在设备的侦查范围内
            long equipmentStart = performanceMonitor.startTimer("equipment_scout");
            List<Map<String, Object>> targetEquipmentRelation = calculateEquipmentScout(filterResult, vo);
            performanceMonitor.endTimer("equipment_scout");

            // 计算任务进度
            long taskProcessStart = performanceMonitor.startTimer("task_process");
            List<RequirementTaskDTO> taskDTOS = taskProcess(vo, targetEquipmentRelation);
            performanceMonitor.endTimer("task_process");

            // 组装结果
            resultMap.put("target", filterResult);
            resultMap.put("satellite", satellite);
            resultMap.put("task", taskDTOS);
            resultMap.put("targetEquipmentRelation", targetEquipmentRelation);
            resultMap.put("totalTime", dataContext.getTotalTime());

            return resultMap;

        } catch (Exception e) {
            log.error("handleDragOrPlay执行失败", e);
            throw new ServiceException("处理拖拽或播放请求失败: " + e.getMessage());
        } finally {
            performanceMonitor.endTimer("handleDragOrPlay");
            performanceProfiler.endProfiling("handleDragOrPlay");
        }
    }

    /**
     * 计算目标是否在设备的侦查 范围内
     * @param resultList
     * @param vo
     * @return
     */
    private List<Map<String, Object>> calculateEquipmentScout(List<DragReturnVO> resultList, DragParamVO vo) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultList)) {
            List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.queryEquipmentByTaskIds(vo.getTaskIds());

            List<String> generaIds = dataEquipmentOccupancies.stream()
                    .map(DataEquipmentOccupancy::getGeneralId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(generaIds)) {
                Map<String, List<DataGeneral>> groupByGneral = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery()
                                .in(DataGeneral::getId, generaIds)
                                .in(DataGeneral::getDataType, Arrays.asList(1, 2, 8))) // 只计算地基平台
                        .stream()
                        .collect(Collectors.groupingBy(DataGeneral::getId));

                List<Map<String, Object>> equipments = dataEquipmentOccupancies.stream()
                        .distinct()
                        .map(data -> {
                            DataGeneral dataGeneral = groupByGneral.get(data.getGeneralId()).get(0);
                            Map<String, Object> equipment = commonMapper.getOne(dataGeneral.getTableName(), data.getEquipmentId());

                            return equipment;
                        })
                        .collect(Collectors.toList());

                for (Map<String, Object> equipment : equipments) {
                    //
                    String longitude = equipment.get("longitude").toString();
                    String latitude = equipment.get("latitude").toString();
                    double radius = Double.parseDouble(equipment.get("radius") != null ? equipment.get("radius").toString() : equipment.get("bottomRadius").toString());

                    for (DragReturnVO dragReturnVO : resultList) {
                        if (CollUtil.isEmpty(dragReturnVO.getPoints())) continue;
                        // 获取当前目标的经纬度
                        PointVO pointVO = dragReturnVO.getPoints().get(0);

                        Point2D.Double equipmentPoint = GISUtils.parsePoint2DDouble(longitude, latitude);
                        Point2D.Double targetPoint = GISUtils.parsePoint2DDouble(pointVO.getX() + "", pointVO.getY() + "");

                        // 计算实时相对距离
                        double distance = GISUtils.getDistanceNew(equipmentPoint, targetPoint);
                        if (distance < radius) {
                            Map<String, Object> relation = new HashMap<>();
                            relation.put("equipmentId", equipment.get("id"));
                            relation.put("targetId", dragReturnVO.getMbId());

                            result.add(relation);
                        }
                    }
                }
            }

        }

        return result;
    }

    /**
     * 计算每个任务的进度
     *
     * @param vo
     * @param targetEquipmentRelation
     */
    private List<RequirementTaskDTO> taskProcess(DragParamVO vo, List<Map<String, Object>> targetEquipmentRelation) {
        // 获取当前时间点
        LocalDateTime currentTime = vo.getStartTime().plusSeconds(vo.getTimePoint());
        Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
        List<Map<String,Object>> relation = new ArrayList<>();
        List<RequirementTaskDTO> taskDTOS = tasks.stream()
                .map(task -> {
                    RequirementTaskDTO taskDTO = task.convert();
                    // 任务开始时间在当前时间之后，说明该任务还没开始
                    Double taskProcess = 0d;


                    IPage<DataEquipmentOccupancyDTO> equipmentByTask = dataEquipmentOccupancyService.getEquipmentByTask(-1, -1, task.getId());
                    long taskDuration = 0;
                    // 大于0说明有资源使用，任务才会继续往下推进
                    if (equipmentByTask.getRecords().size() > 0) {
                        for (DataEquipmentOccupancyDTO occupancyDTO : equipmentByTask.getRecords()) {
                            if (occupancyDTO.getStartTime().isBefore(currentTime) || occupancyDTO.getStartTime().isEqual(currentTime)) {
                                // 判断是否在侦察范围内
                                TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());
                                List<Map<String, Object>> relationTargets = targetEquipmentRelation.stream()
                                        .filter(t -> t.get("targetId").equals(targetRelation.getTargetId()) && t.get("equipmentId").equals(occupancyDTO.getEquipmentId()))
                                        .collect(Collectors.toList());

                                // 判断线
                                if (relationTargets.size() > 0 && occupancyDTO.getEndTime().isAfter(currentTime)) {
                                    relation.addAll(relationTargets);
                                }

                                Duration duration;RequirementTaskDTO.Period period;
                                if (occupancyDTO.getEndTime().isAfter(currentTime)) {
                                    duration = Duration.between(occupancyDTO.getStartTime(), currentTime);

                                    period = new RequirementTaskDTO.Period();
                                    period.setStartTime(occupancyDTO.getStartTime());
                                    period.setEndTime(currentTime);
                                    period.setEquipmentName(occupancyDTO.getEquipmentDetail().get("name").toString());
                                    period.setEquipmentId(occupancyDTO.getEquipmentId());
                                    period.setTargetId(targetRelation.getTargetId());

                                    // 如果大于总时长，说明任务已经完成，进度则为100%
//                                    if (duration.getSeconds() > totalTime.getSeconds()) {
//                                        taskProcess = 1d;
//                                    } else {
//                                        taskProcess = (double) duration.getSeconds() / totalTime.getSeconds();
//                                    }

                                } else {
                                    // 不在范围内说明当前时间已经超出了离开时间
                                    duration = Duration.between(occupancyDTO.getStartTime(), occupancyDTO.getEndTime());

                                    period = new RequirementTaskDTO.Period();
                                    period.setStartTime(occupancyDTO.getStartTime());
                                    period.setEndTime(occupancyDTO.getEndTime());
                                    period.setEquipmentName(occupancyDTO.getEquipmentDetail().get("name").toString());
                                }

                                List<RequirementTaskDTO.Period> periods = taskDTO.getPeriods();
                                periods.add(period);
                                taskDuration += duration.getSeconds();
                            }
                        }
                        // 任务总时长
                        Duration totalTime = Duration.between(task.getStartTime(), task.getEndTime());

                        taskProcess = (double) taskDuration / totalTime.getSeconds();
                        if (taskProcess > 1.0) {
                            taskProcess = 1.0;
                        }
                    }

                    taskDTO.setProcess(Double.parseDouble(String.format("%.2f", taskProcess * 100)));
                    taskDTO.setCurrentTime(currentTime);
                    return taskDTO;
                })
                .collect(Collectors.toList());

        targetEquipmentRelation.clear();
        targetEquipmentRelation.addAll(relation);

        return taskDTOS;
    }

    /**
     * 后置过滤器 根据传入的目标参数、资源参数来过滤最终结果
     * @param resultMap
     * @param vo
     */
    private List<DragReturnVO> postFilterData(List<DragReturnVO> resultMap, DragParamVO vo) {
        List<DragReturnVO> filterResult = resultMap;
        // 获取当前时间点
        LocalDateTime currentTime = vo.getStartTime().plusSeconds(vo.getTimePoint());
        if (vo.getIsSimulate() != null && vo.getIsSimulate()) {

            // 根据目标过滤
            if (StringUtils.isNotBlank(vo.getTargetId())) {
                filterResult = resultMap.stream()
                        .filter(r -> r.getMbId().equals(vo.getTargetId()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(vo.getEquipmentId())) {
                // 获取到关联目标
                List<TaskTargetRelation> targetRelations = getTargetByEquipment(vo.getEquipmentId(), vo.getRequirementId(), currentTime);

                List<String> targetId = targetRelations.stream()
                        .map(TaskTargetRelation::getTargetId)
                        .collect(Collectors.toList());

                filterResult = resultMap.stream()
                        .filter(r -> targetId.contains(r.getMbId()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(vo.getFilterTaskId())) {
                // 查询目标id
                RequirementTask task = requirementTaskService.getById(vo.getFilterTaskId());

                TaskTargetRelation targetRelation = taskTargetService.getById(task.getTargetRelationId());

                filterResult = resultMap.stream()
                        .filter(r -> r.getMbId().equals(targetRelation.getTargetId()))
                        .collect(Collectors.toList());
            }
        }
        return filterResult;
    }

    private List<TaskTargetRelation> getTargetByEquipment(String equipmentId, String requirementId, LocalDateTime currentTime) {
        // 获取当前时间点
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(requirementId);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());
        List<DataEquipmentOccupancy> equipmentOccupancies = dataEquipmentOccupancyService
                .list(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                        .in(DataEquipmentOccupancy::getTaskId, taskIds)
                        .eq(DataEquipmentOccupancy::getEquipmentId, equipmentId)
                        .le(DataEquipmentOccupancy::getStartTime, currentTime)
                        .ge(DataEquipmentOccupancy::getEndTime, currentTime));

        if (CollUtil.isNotEmpty(equipmentOccupancies)) {
            List<String> ids = equipmentOccupancies.stream()
                    .map(DataEquipmentOccupancy::getTaskId)
                    .collect(Collectors.toList());

            List<String> targetRelationIds = requirementTaskService.listByIds(ids).stream()
                    .map(RequirementTask::getTargetRelationId)
                    .collect(Collectors.toList());

//        List<String> targetTaskIds = equipmentOccupancies.stream()
//                .map(DataEquipmentOccupancy::getTaskId)
//                .collect(Collectors.toList());

            // 获取到关联目标
            return new ArrayList<>(taskTargetService.listByIds(targetRelationIds));
        } else {
            return new ArrayList<>();
        }

    }

    private Map<String, Map<String, Object>> satelliteCalculate(DragParamVO vo) {
        Map<String, Object> data = getDataByTasks(vo.getTaskIds());

        Object equip = data.get("equipments");
        if (equip != null) {
            List<DataEquipmentOccupancy> deo = (List<DataEquipmentOccupancy>) equip;
            List<String> generalIds = deo.stream().map(DataEquipmentOccupancy::getGeneralId)
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(generalIds)) return new HashMap<>();
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);

            List<DataGeneral> satellite = dataGenerals.stream()
                    .filter(g -> g.getDataType().equals(DataTypeEnum.SKY_PLATFORM.getCode()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(satellite)) return new HashMap<>();
        }
//        Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
//
//        Map<String, Object> data = new HashMap<>();
//        // 数据聚合
//        for (RequirementTask task : tasks) {
//            Map<String, Object> temp = getData(task.getRequirementId());
//
//            List<TaskWithTargets> targets = data.get("targets") != null ? (List<TaskWithTargets>) data.get("targets") : new ArrayList<>();
//
//            List<TaskWithTargets> tempTarget = temp.get("targets") != null ? (List<TaskWithTargets>) temp.get("targets") : new ArrayList<>();
//            targets.addAll(tempTarget);
//
//            List<DataEquipmentOccupancy> equipments = data.get("equipments") != null ? (List<DataEquipmentOccupancy>) data.get("equipments") : new ArrayList<>();
//
//            List<DataEquipmentOccupancy> tempEquipment = temp.get("equipments") != null ? (List<DataEquipmentOccupancy>) temp.get("equipments") : new ArrayList<>();
//            equipments.addAll(tempEquipment);
//
//            data.put("targets", tempTarget);
//            data.put("equipments", tempEquipment);
//        }

        List<DataEquipmentOccupancy> equipments = (List<DataEquipmentOccupancy>) data.get("equipments");

        // 过滤出天基平台
        List<DataGeneral> dataGenerals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery().eq(DataGeneral::getDataType, 3));

        List<String> generalIds = dataGenerals.stream()
                .map(DataGeneral::getId)
                .collect(Collectors.toList());

        LocalDateTime targetTime = vo.getStartTime().plusSeconds(vo.getTimePoint());

        List<DataEquipmentOccupancy> satellite = equipments.stream()
                .filter(e -> generalIds.contains(e.getGeneralId()))
                .collect(Collectors.toList());

        List<SatelliteDTO> satelliteDTOList = new ArrayList<>();
        for (DataEquipmentOccupancy taskEquipmentRelation : satellite) {
            SatelliteDTO satelliteDTO = new SatelliteDTO();
            satelliteDTO.setEquipmentId(taskEquipmentRelation.getEquipmentId());
            satelliteDTO.setGeneraId(taskEquipmentRelation.getGeneralId());
            satelliteDTO.setDateTime(targetTime);

            satelliteDTOList.add(satelliteDTO);
        }
        Map<String, Map<String, Object>> coordinate = (Map<String, Map<String, Object>>) satelliteService.getSatelliteCoordinate(satelliteDTOList);
        log.info("查询卫星结果：{}", coordinate);

        return coordinate;
    }

    private Integer getTotalTime(DragParamVO dragParamVO) {
        Collection<RequirementTask> tasks = requirementTaskService.listByIds(dragParamVO.getTaskIds());
        LocalDateTime earliestStartTime = null;
        LocalDateTime latestEndTime = null;
        for (RequirementTask task : tasks) {
            if (earliestStartTime == null || task.getStartTime().isBefore(earliestStartTime)) {
                earliestStartTime = task.getStartTime();
            }
            if (latestEndTime == null || task.getEndTime().isAfter(latestEndTime)) {
                latestEndTime = task.getEndTime();
            }
        }

        dragParamVO.setStartTime(earliestStartTime);
        dragParamVO.setEndTime(latestEndTime);
        Duration between = Duration.between(earliestStartTime, latestEndTime);
        log.info("本次推演任务总时长为：{}秒", between.getSeconds());
        if (between.toDays() >= 1) {
            throw new ServiceException("所选择任务总执行时长不能大于一天");
        }
        return (int) between.getSeconds();
    }

    private List<DragReturnVO> processPlay(DragParamVO vo, Integer totalTime) {
        // 计算结束时间点
        Integer endTime = vo.getTimePoint() + vo.getStep();

        // 获取或计算当前时间点的缓存数据
        List<DragReturnVO> result = getCacheOrCompute(vo, vo.getTimePoint(), totalTime);

        if (totalTime == 0) {
            noticeClientMessage("目标数据初始化完成", 50);
            return result;
        }

        // 确定有效的结束时间点
        Integer effectiveEndTime = Math.min(endTime, totalTime);
        // 获取或计算结束时间点的缓存数据
        List<DragReturnVO> nextResult = getCacheOrCompute(vo, effectiveEndTime, totalTime);
        noticeClientMessage("正在缓存数据...", 30);

        // 计算分割片数
        Integer shard = (effectiveEndTime - vo.getTimePoint()) * SHARD_VALUE;

        // 返回切割后的结果
        return getShardResult(result, nextResult, shard);
    }

    private void noticeClientMessage(String message, Integer process) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("message", message);
        msg.put("process", (double) process / 100);

        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(msg);
        dto.setType(WebSocketTypeEnum.PROCESS.getCode());
        globalServer.sendAll(JSON.toJSONString(dto));
    }

    private List<DragReturnVO> processDrag(DragParamVO vo, Integer totalTime) {
        long startTime = performanceMonitor.startTimer("processDrag");

        try {
            String cacheKey = POINT_CACHE_PREFIX + vo.getBzId();

            // 使用简化的缓存策略
            String pointData = simpleCacheStrategy.getTimePointDataSimple(
                cacheKey, vo.getTimePoint(), totalTime, this, vo.getTaskIds());

            List<DragReturnVO> result = JsonUtils.json2List(pointData, DragReturnVO.class);

            return totalTime == 0 ? result : filterImportantPoints(result);

        } finally {
            performanceMonitor.endTimer("processDrag");
        }
    }

    /**
     * 获取基础数据上下文，一次性查询所有需要的数据
     */
    private BaseDataContext getBaseDataContext(DragParamVO vo) {
        long startTime = performanceMonitor.startTimer("getBaseDataContext");

        try {
            BaseDataContext context = new BaseDataContext(vo.getTaskIds());

            // 1. 查询任务数据并计算总时长
            Collection<RequirementTask> tasks = requirementTaskService.listByIds(vo.getTaskIds());
            context.setTasks(new ArrayList<>(tasks));

            // 计算总时长
            LocalDateTime earliestStartTime = null;
            LocalDateTime latestEndTime = null;
            for (RequirementTask task : tasks) {
                if (earliestStartTime == null || task.getStartTime().isBefore(earliestStartTime)) {
                    earliestStartTime = task.getStartTime();
                }
                if (latestEndTime == null || task.getEndTime().isAfter(latestEndTime)) {
                    latestEndTime = task.getEndTime();
                }
            }

            context.setStartTime(earliestStartTime);
            context.setEndTime(latestEndTime);
            vo.setStartTime(earliestStartTime);
            vo.setEndTime(latestEndTime);

            Duration between = Duration.between(earliestStartTime, latestEndTime);
            if (between.toDays() >= 1) {
                throw new ServiceException("所选择任务总执行时长不能大于一天");
            }
            context.setTotalTime((int) between.getSeconds());

            // 2. 获取任务相关的所有数据
            Map<String, Object> rawData = getDataByTasksWithCache(vo.getTaskIds());
            context.setRawData(rawData);

            // 3. 解析并设置各种数据
            List<TaskWithTargets> taskWithTargets = JsonConvertUtil.getTaskWithTargetsList(rawData);
            context.setTaskWithTargets(taskWithTargets);

            List<DataEquipmentOccupancy> equipments = JsonConvertUtil.getDataEquipmentOccupancyList(rawData);
            context.setEquipments(equipments);

            // 4. 查询通用数据（包含卫星）
            if (!equipments.isEmpty()) {
                List<String> generalIds = equipments.stream()
                    .map(DataEquipmentOccupancy::getGeneralId)
                    .distinct()
                    .collect(Collectors.toList());

                if (!generalIds.isEmpty()) {
                    Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);
                    context.setDataGenerals(new ArrayList<>(dataGenerals));

                    // 过滤出卫星数据
                    List<DataGeneral> satellites = dataGenerals.stream()
                        .filter(g -> g.getDataType().equals(DataTypeEnum.SKY_PLATFORM.getCode()))
                        .collect(Collectors.toList());
                    context.setSatellites(satellites);
                }
            }

            log.info("基础数据上下文构建完成，任务数: {}, 总时长: {}秒",
                tasks.size(), context.getTotalTime());

            return context;

        } finally {
            performanceMonitor.endTimer("getBaseDataContext");
        }
    }

    @Override
    public String computeTimePointData(List<String> taskIds, Integer timePoint) {
        return getPointDataForTimePoint(taskIds, timePoint);
    }

    private List<DragReturnVO> getCacheOrCompute(DragParamVO vo, Integer timePoint, Integer totalTime) {
        long startTime = performanceMonitor.startTimer("getCacheOrCompute");

        try {
            // 获取缓存的键
            String cacheKey = POINT_CACHE_PREFIX + vo.getBzId();

            // 使用简化的缓存策略，避免复杂的后台计算
            String pointData = simpleCacheStrategy.getTimePointDataSimple(
                cacheKey, timePoint, totalTime, this, vo.getTaskIds());

            return JsonUtils.json2List(pointData, DragReturnVO.class);

        } finally {
            performanceMonitor.endTimer("getCacheOrCompute");
        }
    }

    /**
     * 缓存单个时间点的数据
     */
    private void cacheSingleTimePoint(String requirementId, Integer timePoint, String data, Integer totalTime) {
        String cacheKey = POINT_CACHE_PREFIX + requirementId;
        RedisUtil.HashOps.hPut(cacheKey, String.valueOf(timePoint), data);

        int millis = totalTime * 1000 * performanceConfig.getPointDataCacheMultiplier();
        if (millis > -1) {
            RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 批量缓存多个时间点的数据
     */
    private void cacheBatchTimePoints(String requirementId, Map<String, String> pointMap, Integer totalTime) {
        String cacheKey = POINT_CACHE_PREFIX + requirementId;
        RedisUtil.HashOps.hPutAll(cacheKey, pointMap);

        int millis = totalTime * 1000 * performanceConfig.getPointDataCacheMultiplier();
        if (millis > -1) {
            RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
        }
    }

    // 缓存已计算的航迹数据，避免重复计算
    private final ConcurrentHashMap<String, List<DragReturnVO>> trackCache = new ConcurrentHashMap<>();

    /**
     * 优化的时间点数据计算方法
     */
    private String getPointDataForTimePoint(List<String> taskIds, Integer timePoint) {
        long startTime = performanceMonitor.startTimer("getPointDataForTimePoint");

        try {
            // 生成缓存key
            String trackCacheKey = generateTrackCacheKey(taskIds);

            // 尝试从航迹缓存获取
            List<DragReturnVO> allTracks = trackCache.get(trackCacheKey);

            if (allTracks == null) {
                // 首次计算，需要查询数据库和生成航迹
                Map<String, Object> data = getDataByTasksWithCache(taskIds);
                List<TaskWithTargets> targets = JsonConvertUtil.getTaskWithTargetsList(data);
                allTracks = obtainTrack(targets);

                // 缓存航迹数据（5分钟）
                trackCache.put(trackCacheKey, allTracks);

                // 异步清理过期缓存
                CompletableFuture.runAsync(() -> {
                    try {
                        Thread.sleep(5 * 60 * 1000); // 5分钟后清理
                        trackCache.remove(trackCacheKey);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            }

            // 快速计算当前时间点的数据
            List<DragReturnVO> result = new ArrayList<>();
            for (DragReturnVO item : allTracks) {
                DragReturnVO vo = new DragReturnVO();
                vo.setMbId(item.getMbId());
                vo.setSlhId(item.getSlhId());
                vo.setStartDate(item.getStartDate());
                vo.setType(item.getType());
                vo.setPoints(item.getPlayPoints(0, timePoint));
                vo.setIsOnlyOne(item.getIsOnlyOne());
                vo.setZbbm(item.getZbbm());
                result.add(vo);
            }

            return JSON.toJSONString(result);

        } finally {
            performanceMonitor.endTimer("getPointDataForTimePoint");
        }
    }

    /**
     * 生成航迹缓存key
     */
    private String generateTrackCacheKey(List<String> taskIds) {
        // 对taskIds排序后生成hash，确保相同的taskIds组合得到相同的key
        List<String> sortedIds = new ArrayList<>(taskIds);
        Collections.sort(sortedIds);
        return "track_" + String.join(",", sortedIds).hashCode();
    }

    /**
     * 优化的卫星计算方法，使用已查询的数据
     */
    private Map<String, Map<String, Object>> satelliteCalculateOptimized(BaseDataContext dataContext) {
        long startTime = performanceMonitor.startTimer("satelliteCalculateOptimized");

        try {
            // 使用已查询的卫星数据，避免重复查询
            List<DataGeneral> satellites = dataContext.getSatellites();
            if (CollUtil.isEmpty(satellites)) {
                return new HashMap<>();
            }

            List<DataEquipmentOccupancy> equipments = dataContext.getEquipments();
            if (CollUtil.isEmpty(equipments)) {
                return new HashMap<>();
            }

            // 过滤出天基平台相关的设备
            List<String> satelliteIds = satellites.stream()
                .map(DataGeneral::getId)
                .collect(Collectors.toList());

            List<DataEquipmentOccupancy> satelliteEquipments = equipments.stream()
                .filter(eq -> satelliteIds.contains(eq.getGeneralId()))
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(satelliteEquipments)) {
                return new HashMap<>();
            }

            // 执行卫星计算逻辑
            Map<String, Map<String, Object>> coordinate = new HashMap<>();

            for (DataEquipmentOccupancy equipment : satelliteEquipments) {
                DataGeneral satellite = satellites.stream()
                    .filter(s -> s.getId().equals(equipment.getGeneralId()))
                    .findFirst()
                    .orElse(null);

                if (satellite != null) {
                    Map<String, Object> satelliteData = calculateSatellitePosition(satellite, equipment);
                    coordinate.put(equipment.getId(), satelliteData);
                }
            }

            log.info("卫星计算完成，计算数量: {}", coordinate.size());
            return coordinate;

        } finally {
            performanceMonitor.endTimer("satelliteCalculateOptimized");
        }
    }

    /**
     * 计算单个卫星位置（简化版本，避免复杂计算）
     */
    private Map<String, Object> calculateSatellitePosition(DataGeneral satellite, DataEquipmentOccupancy equipment) {
        Map<String, Object> result = new HashMap<>();

        // 这里可以根据实际需求实现卫星位置计算
        // 为了性能考虑，可以使用缓存或简化计算
        result.put("satelliteId", satellite.getId());
        result.put("equipmentId", equipment.getId());
        result.put("position", "calculated_position"); // 实际计算逻辑

        return result;
    }

    /**
     * 批量预计算关键时间点的数据（异步执行）
     */
    private void precomputeKeyTimePoints(List<String> taskIds, Integer totalTime, String cacheKey) {
        // 检查是否启用异步预计算
        if (!performanceConfig.isEnableAsyncPrecompute()) {
            return;
        }

        // 根据配置计算关键时间点
        List<Integer> keyTimePoints = new ArrayList<>();
        for (double ratio : performanceConfig.getKeyTimePointRatios()) {
            keyTimePoints.add((int) (totalTime * ratio));
        }

        // 异步预计算，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                Map<String, Object> data = getDataByTasksWithCache(taskIds);
                // 使用安全的类型转换
                List<TaskWithTargets> targets = JsonConvertUtil.getTaskWithTargetsList(data);
                List<DragReturnVO> allTracks = obtainTrack(targets);

                Map<String, String> precomputedData = new HashMap<>();
                for (Integer timePoint : keyTimePoints) {
                    if (timePoint >= 0 && timePoint <= totalTime) {
                        List<DragReturnVO> result = new ArrayList<>();
                        allTracks.forEach(item -> {
                            DragReturnVO vo = new DragReturnVO();
                            vo.setMbId(item.getMbId());
                            vo.setSlhId(item.getSlhId());
                            vo.setStartDate(item.getStartDate());
                            vo.setType(item.getType());
                            vo.setPoints(item.getPlayPoints(0, timePoint));
                            vo.setIsOnlyOne(item.getIsOnlyOne());
                            vo.setZbbm(item.getZbbm());
                            result.add(vo);
                        });
                        precomputedData.put(String.valueOf(timePoint), JSON.toJSONString(result));
                    }
                }

                // 批量存入缓存
                if (!precomputedData.isEmpty()) {
                    RedisUtil.HashOps.hPutAll(cacheKey, precomputedData);
                    int millis = totalTime * 1000 * performanceConfig.getPointDataCacheMultiplier();
                    if (millis > -1) {
                        RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
                    }
                }

                log.info("预计算关键时间点完成，缓存key: {}, 时间点: {}", cacheKey, keyTimePoints);
            } catch (Exception e) {
                log.error("预计算关键时间点失败", e);
            }
        });
    }

    private List<DragReturnVO> obtainTrack(List<TaskWithTargets> targets) {
        // 首先需要计算出所有目标最早的起飞时间，以这个时间作为起始时间点0
        targets = calculateEarlyTime(targets);

        List<DragReturnVO> allTracks = new ArrayList<>();
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetWithTracks = target.getTargets();
            for (TargetWithTracks targetWithTrack : targetWithTracks) {
                List<PointVO> keyPoints;
                if (CollUtil.isEmpty(targetWithTrack.getTracks())) {
                    // 如果该目标航迹为空,则填入目标位置
                    keyPoints = handleEmptyTargetTrack(targetWithTrack.getTarget());
                } else {
                    // 根据时间对航迹进行排序
                    keyPoints = targetWithTrack.getTracks().stream()
                            .sorted(Comparator.comparing(RequirementTargetTrack::getTime))
                            .map(track -> new PointVO(track.getLongitude().doubleValue(), track.getLatitude().doubleValue(),
                                    track.getAltitude().doubleValue(), track.getTime() + target.getRelativeTime(), track.getDirect().doubleValue(),
                                    track.getSpeed().doubleValue(), true)
                            )
                            .collect(Collectors.toList());

                    // 不为0则增加一个时间点为0的关键点，此点可看为静止点
                    if (target.getRelativeTime() != 0) {
                        PointVO point = keyPoints.get(0);

                        PointVO pointVO = new PointVO();
                        BeanUtil.copyProperties(point, pointVO);
                        pointVO.setTimePoint(0);

                        keyPoints.add(pointVO);

                        keyPoints = keyPoints.stream()
                                .sorted(Comparator.comparing(PointVO::getTimePoint)).collect(Collectors.toList());
                    }
                }
                TrackVO trackVO = new TrackVO();

                trackVO.setPoints(keyPoints);
                // 获取航迹第一个点的时间即为开始时间
                trackVO.setStartTime(keyPoints.get(0).getTimePoint());

                // 生成关键点以及插值点
                List<PointVO> allPoints = generateLinePoints(trackVO);

                DragReturnVO result = new DragReturnVO();
                result.setMbId(targetWithTrack.getTarget().getTargetId());
                result.setPoints(allPoints);
                result.setStartDate(trackVO.getPoints().get(0).getTimePoint());

                allTracks.add(result);
            }
        }

        return allTracks;
    }

    private List<TaskWithTargets> calculateEarlyTime(List<TaskWithTargets> targets) {
        LocalDateTime earlyTime = null;
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetTargets = target.getTargets();
            TaskTargetRelation targetRelation = targetTargets.get(0).getTarget();

            if (earlyTime == null) {
                earlyTime = targetRelation.getStartTime();
            } else if (targetRelation.getStartTime().isBefore(earlyTime)) {
                earlyTime = targetRelation.getStartTime();
            }
        }

        // 計算相对时间
        for (TaskWithTargets target : targets) {
            List<TargetWithTracks> targetTargets = target.getTargets();
            TaskTargetRelation targetRelation = targetTargets.get(0).getTarget();

            Duration between = Duration.between(earlyTime, targetRelation.getStartTime());

            target.setRelativeTime((int) between.getSeconds());
        }

        // 去重
        return new ArrayList<>(targets.stream()
                .collect(Collectors.toMap(
                        TaskWithTargets::getTargetId,
                        Function.identity(),
                        (k1, k2) -> k1
                ))
                .values());
    }

    private List<PointVO> generateLinePoints(TrackVO trackVO) {
        List<PointVO> points = new ArrayList<>();
        if (trackVO.getPoints().size() > 1) {
            List<PointVO> keyPoints = trackVO.getPoints();
            //第一个关键点添加到返回结果中
            PointVO startPoint = keyPoints.get(0);
            startPoint.setPrimary(true);
            points.add(startPoint);
            try {
                for (int i = 0; i < keyPoints.size(); i++) {
                    if (i != keyPoints.size() - 1) {
                        PointVO start = keyPoints.get(i);
                        PointVO end = keyPoints.get(i + 1);
                        int seconds = end.getTimePoint() - start.getTimePoint();
                        List<PointVO> pointVos = createInterpolationPoints(start, end, trackVO.getStartTime(), seconds, trackVO);
                        points.addAll(pointVos);
                    }
                }
                //把最后一个关键点添加到返回结果中
                PointVO endPoint = keyPoints.get(keyPoints.size() - 1);
                endPoint.setPrimary(true);
                points.add(endPoint);
                log.debug("point - > size -> {}", points.size());
            } catch (Exception e) {
                log.error("{} -> TrackServiceImpl -> method generateLinePoints is failed ", JSON.toJSONString(e));
                log.error("发生异常", e);
            }
        } else {
            PointVO pointVo = trackVO.getPoints().get(0);
            pointVo.setPrimary(true);
            points = Collections.singletonList(pointVo);
        }
        return points;
    }

    private List<PointVO> createInterpolationPoints(PointVO start, PointVO end, int beginTime, int seconds, TrackVO vo) {
        List<PointVO> points = new ArrayList<>(seconds + 1);
        double startX = start.getX();
        double startY = start.getY();
        double endX = end.getX();
        double endY = end.getY();
        //起止点经度差值
        double distanceX = endX - startX;
        //起止点纬度差值
        double distanceY = endY - startY;
        //单位时间内移动的经度
        double oneUnitX = distanceX / seconds;
        //单位时间内移动的纬度
        double oneUnitY = distanceY / seconds;
        //该两点所确定直线的方位角
        double angle = BsairUtil.getPointAngle(startX, startY, endX, endY);
        //设置各关键点角度信息
        start.setAngle(angle);
        end.setAngle(angle);
        //计算速度
        double speed = getSpeed(start, end, seconds);
        //设置各关键点速度
        end.setSpeed(speed);
        start.setSpeed(speed);
        //循环生成关键点信息并添加
        for (int k = 1; k < seconds; k++) {
            beginTime++;
            if (beginTime == start.getTimePoint()) {
                start.setPrimary(true);
                points.add(start);
                vo.setStartTime(beginTime);
                k = k - 1;
                continue;
            }
            if (beginTime == end.getTimePoint()) {
                end.setPrimary(true);
                points.add(end);
                vo.setStartTime(beginTime);
                k = k - 1;
                continue;
            }
            points.add(new PointVO(startX + oneUnitX * k,
                    startY + oneUnitY * k, start.getZ(), beginTime, angle, speed, false));
        }
        vo.setStartTime(beginTime);
        return points;
    }

    private double getSpeed(PointVO start, PointVO end, int seconds) {
        Point2D.Double source = GISUtils.parsePoint2DDouble(String.valueOf(start.getX()), String.valueOf((start.getY())));
        Point2D.Double target = GISUtils.parsePoint2DDouble(String.valueOf(end.getX()), String.valueOf((end.getY())));
        double distance = GISUtils.getDistanceNew(source, target);
        return (distance * 3600) / (seconds * 1000);
    }

    private List<PointVO> handleEmptyTargetTrack(TaskTargetRelation target) {
        PointVO point = new PointVO(target.getLongitude().doubleValue(), target.getLatitude().doubleValue(), target.getAltitude().doubleValue(),
                0, 0, 0, true);
        return Collections.singletonList(point);
    }

    private List<Object> getTimePointsUpTo(Integer currentTimePoint) {
        // 获取从0到当前时间点的所有时间点列表
        return IntStream.rangeClosed(0, currentTimePoint).boxed().map(String::valueOf).collect(Collectors.toList());
    }

    private void populateResultFromCache(List<Object> cachedData, List<DragReturnVO> result) {
        // 将缓存的数据转换为对象并添加到结果列表中
        for (Object data : cachedData) {
            if (Objects.isNull(data)) continue;
            result.addAll(JsonUtils.json2List(data.toString(), DragReturnVO.class));
        }
    }

    private List<DragReturnVO> filterImportantPoints(List<DragReturnVO> points) {
        // 按目标ID分组，并将所有点合并为一个对象
        return points.stream()
                .collect(Collectors.groupingBy(DragReturnVO::getMbId))
                .values().stream()
                .map(group -> {
                    DragReturnVO result = new DragReturnVO();
                    DragReturnVO first = group.get(0);
                    BeanUtils.copyProperties(first, result);
                    result.setPoints(group.stream().flatMap(v -> v.getPoints().stream()).collect(Collectors.toList()));
                    return result;
                })
                .collect(Collectors.toList());
    }

    private void fillDragPoints(List<Object> timePoints, Map<String, String> pointMap, List<DragReturnVO> result) {
        // 根据时间点列表从点迹映射中获取数据并添加到结果列表中
        for (Object timePoint : timePoints) {
            String pointStr = pointMap.get(timePoint.toString());
            if (pointStr != null) {
                result.addAll(JsonUtils.json2List(pointStr, DragReturnVO.class));
            }
        }
    }

    private List<DragReturnVO> getShardResult(List<DragReturnVO> originResult, List<DragReturnVO> nextResult, Integer shard) {
        noticeClientMessage("目标数据初始化完成...", 50);
        return originResult.stream()
                .map(originVo -> {
                    if (shard == 0) {
                        return originVo;
                    }
                    if (originVo.getIsOnlyOne() != null && originVo.getIsOnlyOne()) {
                        return originVo;
                    }
                    // 获取原始点和下一个点的坐标
                    PointVO originPoint = originVo.getPoints().get(0);
                    DragReturnVO nextVo = nextResult.stream()
                            .filter(vo -> vo.getMbId().equals(originVo.getMbId()))
                            .findFirst()
                            .orElse(null);

                    if (nextVo == null) return originVo;

                    PointVO nextPoint = nextVo.getPoints().get(0);
                    // 计算每个分割点的经度和纬度增量
                    double xStep = (nextPoint.getX() - originPoint.getX()) / shard;
                    double yStep = (nextPoint.getY() - originPoint.getY()) / shard;

                    // 生成分割点列表
                    List<PointVO> interpolatedPoints = IntStream.range(0, shard)
                            .mapToObj(i -> new PointVO(
                                    originPoint.getX() + i * xStep,
                                    originPoint.getY() + i * yStep,
                                    originPoint.getZ(),
                                    originPoint.getTimePoint(),
                                    originPoint.getAngle(),
                                    0,
                                    null
                            ))
                            .collect(Collectors.toList());

                    DragReturnVO interpolatedVo = new DragReturnVO();
                    BeanUtils.copyProperties(originVo, interpolatedVo);
                    interpolatedVo.setPoints(interpolatedPoints);
                    return interpolatedVo;
                })
                .collect(Collectors.toList());
    }
}
