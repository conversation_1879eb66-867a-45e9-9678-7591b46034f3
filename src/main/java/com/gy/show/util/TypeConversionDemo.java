package com.gy.show.util;

import com.alibaba.fastjson.JSON;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.TaskWithTargets;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 类型转换问题演示和解决方案
 */
@Slf4j
public class TypeConversionDemo {
    
    public static void main(String[] args) {
        demonstrateTypeCastingProblem();
        demonstrateSolution();
    }
    
    /**
     * 演示类型转换问题
     */
    public static void demonstrateTypeCastingProblem() {
        log.info("=== 演示类型转换问题 ===");
        
        // 创建测试数据
        RequirementTask task = new RequirementTask();
        task.setId("task1");
        task.setTaskName("测试任务");
        task.setStartTime(LocalDateTime.now());
        
        TaskWithTargets taskWithTargets = new TaskWithTargets(task, new ArrayList<>());
        taskWithTargets.setTargetId("target1");
        
        List<TaskWithTargets> originalList = Arrays.asList(taskWithTargets);
        
        // 模拟缓存存储和读取过程
        Map<String, Object> data = new HashMap<>();
        data.put("targets", originalList);
        
        // 序列化到JSON（模拟存入缓存）
        String jsonString = JSON.toJSONString(data);
        log.info("序列化后的JSON: {}", jsonString);
        
        // 反序列化（模拟从缓存读取）
        Map<String, Object> deserializedData = JSON.parseObject(jsonString, Map.class);
        
        try {
            // 这里会抛出ClassCastException
            @SuppressWarnings("unchecked")
            List<TaskWithTargets> targets = (List<TaskWithTargets>) deserializedData.get("targets");
            log.info("直接强制转换成功: {}", targets.size());
        } catch (ClassCastException e) {
            log.error("直接强制转换失败: {}", e.getMessage());
        }
    }
    
    /**
     * 演示解决方案
     */
    public static void demonstrateSolution() {
        log.info("=== 演示解决方案 ===");
        
        // 创建测试数据
        RequirementTask task = new RequirementTask();
        task.setId("task1");
        task.setTaskName("测试任务");
        task.setStartTime(LocalDateTime.now());
        
        TaskWithTargets taskWithTargets = new TaskWithTargets(task, new ArrayList<>());
        taskWithTargets.setTargetId("target1");
        
        List<TaskWithTargets> originalList = Arrays.asList(taskWithTargets);
        
        // 模拟缓存存储和读取过程
        Map<String, Object> data = new HashMap<>();
        data.put("targets", originalList);
        
        // 序列化到JSON（模拟存入缓存）
        String jsonString = JSON.toJSONString(data);
        
        // 反序列化（模拟从缓存读取）
        Map<String, Object> deserializedData = JSON.parseObject(jsonString, Map.class);
        
        // 使用安全的转换方法
        List<TaskWithTargets> targets = JsonConvertUtil.getTaskWithTargetsList(deserializedData);
        
        log.info("安全转换成功，数量: {}", targets.size());
        if (!targets.isEmpty()) {
            TaskWithTargets first = targets.get(0);
            log.info("第一个任务ID: {}", first.getTask().getId());
            log.info("第一个任务名称: {}", first.getTask().getTaskName());
            log.info("目标ID: {}", first.getTargetId());
        }
    }
}
