package com.gy.show.util;

import com.alibaba.fastjson.JSON;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.TaskWithTargets;
import com.gy.show.entity.dto.TargetWithTracks;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonConvertUtil测试类
 */
public class JsonConvertUtilTest {
    
    @Test
    public void testGetTaskWithTargetsList() {
        // 创建测试数据
        RequirementTask task = new RequirementTask();
        task.setId("task1");
        task.setTaskName("测试任务");
        task.setStartTime(LocalDateTime.now());
        
        TaskWithTargets taskWithTargets = new TaskWithTargets(task, new ArrayList<>());
        taskWithTargets.setTargetId("target1");
        
        List<TaskWithTargets> originalList = Arrays.asList(taskWithTargets);
        
        // 模拟缓存序列化/反序列化过程
        String jsonString = JSON.toJSONString(originalList);
        Object deserializedObj = JSON.parse(jsonString);
        
        Map<String, Object> data = new HashMap<>();
        data.put("targets", deserializedObj);
        
        // 测试转换
        List<TaskWithTargets> result = JsonConvertUtil.getTaskWithTargetsList(data);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getTask().getId());
        assertEquals("target1", result.get(0).getTargetId());
    }
    
    @Test
    public void testGetDataEquipmentOccupancyList() {
        // 创建测试数据
        DataEquipmentOccupancy equipment = new DataEquipmentOccupancy();
        equipment.setId("eq1");
        equipment.setEquipmentId("equipment1");
        equipment.setTaskId("task1");
        
        List<DataEquipmentOccupancy> originalList = Arrays.asList(equipment);
        
        // 模拟缓存序列化/反序列化过程
        String jsonString = JSON.toJSONString(originalList);
        Object deserializedObj = JSON.parse(jsonString);
        
        Map<String, Object> data = new HashMap<>();
        data.put("equipments", deserializedObj);
        
        // 测试转换
        List<DataEquipmentOccupancy> result = JsonConvertUtil.getDataEquipmentOccupancyList(data);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("eq1", result.get(0).getId());
        assertEquals("equipment1", result.get(0).getEquipmentId());
    }
    
    @Test
    public void testGetTaskWithTargetsListWithNull() {
        Map<String, Object> data = new HashMap<>();
        data.put("targets", null);
        
        List<TaskWithTargets> result = JsonConvertUtil.getTaskWithTargetsList(data);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    public void testGetTaskWithTargetsListWithEmptyMap() {
        Map<String, Object> data = new HashMap<>();
        
        List<TaskWithTargets> result = JsonConvertUtil.getTaskWithTargetsList(data);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    public void testConvertObject() {
        RequirementTask task = new RequirementTask();
        task.setId("task1");
        task.setTaskName("测试任务");
        
        // 模拟JSON序列化/反序列化
        String jsonString = JSON.toJSONString(task);
        Object obj = JSON.parse(jsonString);
        
        RequirementTask result = JsonConvertUtil.convertObject(obj, RequirementTask.class);
        
        assertNotNull(result);
        assertEquals("task1", result.getId());
        assertEquals("测试任务", result.getTaskName());
    }
    
    @Test
    public void testConvertList() {
        RequirementTask task1 = new RequirementTask();
        task1.setId("task1");
        
        RequirementTask task2 = new RequirementTask();
        task2.setId("task2");
        
        List<RequirementTask> originalList = Arrays.asList(task1, task2);
        
        // 模拟JSON序列化/反序列化
        String jsonString = JSON.toJSONString(originalList);
        Object obj = JSON.parse(jsonString);
        
        List<RequirementTask> result = JsonConvertUtil.convertList(obj, RequirementTask.class);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("task1", result.get(0).getId());
        assertEquals("task2", result.get(1).getId());
    }
}
